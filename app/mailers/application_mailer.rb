class ApplicationMailer < ActionMailer::Base
  class SuspendDelivery < StandardError; end

  include Utilities::Strings
  include Utilities::Domains
  include Utilities::Datetimes
  include EmailFormatHelper
  include EmailLimit

  helper Utilities::Domains
  helper EmailFormatHelper

  default from: "Genuity Support <<EMAIL>>",
          bcc: Rails.application.credentials.bcc_mail_address
  layout 'nulodgic_mailer'

  after_action :prevent_delivery_for_sample_companies
  rescue_from SuspendDelivery, with: -> {}

  def mail(args)
    # verify_mail_delivery
    super(args)
    create_email_log(args)
  end

  def verify_mail_delivery
    raise SuspendDelivery.new if Rails.application.credentials.suspend_emails == "true" || GlobalEmailBlocking.last&.company_ids&.include?(@company&.id)
    unless self.class == FreeTrialMailer || self.class == SubscriptionMailer || self.action_name == "confirmation_instructions"
      raise SuspendDelivery.new unless !@company || @company.allow_access?
    end
    if self.action_name != "invitation_email"
      raise SuspendDelivery.new if @company_user && @company_user.granted_access_at.nil?
    end
    if @company.present? && self.action_name != 'email_sending_limit_reached' && company_sent_emails.count >= company_allowed_limit
      if check_alert_email.count == 0
        EmailLimitReachedMailer.email_sending_limit_reached(@company.id, company_allowed_limit).deliver_now
        NotifySlackEmailLimitReachWorker.perform_async(@company['id'])
      end
      raise SuspendDelivery.new
    end
  end

  def prevent_delivery_for_sample_companies
    raise SuspendDelivery.new if @company&.is_sample_company?
  end

  def create_email_log(args)
    begin
      Array(args[:to]).each do |recipient|
        Logs::EmailLog.create(
          company_id: @company&.id,
          subject: args[:subject],
          body: args[:body] || args[:email_body],
          sender_email: args[:from],
          receiver_emails: [recipient],
          created_at: DateTime.now
        )
      end
    rescue Exception => e
      Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    end
  end

  def check_alert_email
    company_sent_emails.where(subject: "Daily Email Sending Limit Reached for #{@company.name}")
  end

  def company_sent_emails
    @company_sent_emails ||= Logs::EmailLog.where(company_id: @company.id, created_at: DateTime.now.all_day)
  end

  def company
    @company
  end
end
